@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom GPS Settings Slider Styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 4px;
  outline: none;
  transition: all 0.2s ease-in-out;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease-in-out;
}

.slider::-webkit-slider-thumb:hover {
  background: #2563eb;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease-in-out;
}

.slider::-moz-range-thumb:hover {
  background: #2563eb;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Dark mode slider styles */
@media (prefers-color-scheme: dark) {
  .slider::-webkit-slider-thumb {
    border: 2px solid #374151;
  }

  .slider::-moz-range-thumb {
    border: 2px solid #374151;
  }
}

/* GPS Accuracy Monitor animations */
@keyframes gps-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.gps-pulse {
  animation: gps-pulse 2s ease-in-out infinite;
}

/* Smooth transitions for GPS components */
.gps-transition {
  transition: all 0.3s ease-in-out;
}

/* GPS Settings expand/collapse animation */
.gps-settings-content {
  transition: max-height 0.3s ease-in-out, opacity 0.2s ease-in-out;
  overflow: hidden;
}

.gps-settings-content.expanded {
  max-height: 1000px;
  opacity: 1;
}

.gps-settings-content.collapsed {
  max-height: 0;
  opacity: 0;
}
