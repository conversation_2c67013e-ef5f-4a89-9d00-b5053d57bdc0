/**
 * GPS Accuracy Monitor Component
 * Provides real-time GPS accuracy feedback and suggestions to users
 */

import React, { useState, useEffect, useCallback } from 'react';
import { geolocationService } from '../services/geolocationService';

interface AccuracyFeedback {
  level: 'excellent' | 'good' | 'fair' | 'poor';
  message: string;
  suggestions: string[];
}

interface GPSAccuracyMonitorProps {
  isActive: boolean;
  onAccuracyChange?: (accuracy: number, feedback: AccuracyFeedback) => void;
  showFeedback?: boolean;
  className?: string;
}

export const GPSAccuracyMonitor: React.FC<GPSAccuracyMonitorProps> = ({
  isActive,
  onAccuracyChange,
  showFeedback = true,
  className = ''
}) => {
  const [accuracy, setAccuracy] = useState<number | null>(null);
  const [feedback, setFeedback] = useState<AccuracyFeedback | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);

  const handleAccuracyUpdate = useCallback((newAccuracy: number, newFeedback: AccuracyFeedback) => {
    setAccuracy(newAccuracy);
    setFeedback(newFeedback);
    onAccuracyChange?.(newAccuracy, newFeedback);
  }, [onAccuracyChange]);

  useEffect(() => {
    if (!isActive || isMonitoring) return;

    setIsMonitoring(true);
    const cleanup = geolocationService.startAccuracyMonitoring(handleAccuracyUpdate);

    return () => {
      cleanup();
      setIsMonitoring(false);
    };
  }, [isActive, isMonitoring, handleAccuracyUpdate]);

  const getAccuracyColor = (level: string): string => {
    switch (level) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getAccuracyIcon = (level: string): string => {
    switch (level) {
      case 'excellent': return '🎯';
      case 'good': return '✅';
      case 'fair': return '⚠️';
      case 'poor': return '❌';
      default: return '📍';
    }
  };

  if (!isActive || !showFeedback || !feedback) {
    return null;
  }

  return (
    <div className={`gps-accuracy-monitor ${className}`}>
      <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border">
        <span className="text-lg">{getAccuracyIcon(feedback.level)}</span>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span className={`font-medium ${getAccuracyColor(feedback.level)}`}>
              {feedback.message}
            </span>
            {accuracy !== null && (
              <span className="text-sm text-gray-500">
                (±{Math.round(accuracy)}m)
              </span>
            )}
          </div>
          
          {feedback.suggestions.length > 0 && (
            <div className="mt-2">
              <p className="text-sm text-gray-600 mb-1">Suggestions:</p>
              <ul className="text-sm text-gray-600 space-y-1">
                {feedback.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * GPS Accuracy Badge - Compact version for showing current accuracy
 */
interface GPSAccuracyBadgeProps {
  accuracy: number | null;
  className?: string;
}

export const GPSAccuracyBadge: React.FC<GPSAccuracyBadgeProps> = ({
  accuracy,
  className = ''
}) => {
  if (accuracy === null) return null;

  const feedback = geolocationService.getAccuracyFeedback(accuracy);
  const color = feedback.level === 'excellent' ? 'bg-green-100 text-green-800' :
                feedback.level === 'good' ? 'bg-blue-100 text-blue-800' :
                feedback.level === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800';

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${color} ${className}`}>
      <span className="mr-1">{getAccuracyIcon(feedback.level)}</span>
      ±{Math.round(accuracy)}m
    </span>
  );
};

/**
 * GPS Warm-up Button - Helps improve initial GPS accuracy
 */
interface GPSWarmupButtonProps {
  onWarmupComplete?: () => void;
  className?: string;
}

export const GPSWarmupButton: React.FC<GPSWarmupButtonProps> = ({
  onWarmupComplete,
  className = ''
}) => {
  const [isWarming, setIsWarming] = useState(false);

  const handleWarmup = async () => {
    setIsWarming(true);
    try {
      await geolocationService.warmUpGPS();
      onWarmupComplete?.();
    } catch (error) {
      console.error('GPS warmup failed:', error);
    } finally {
      setIsWarming(false);
    }
  };

  return (
    <button
      onClick={handleWarmup}
      disabled={isWarming}
      className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      {isWarming ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Warming up GPS...
        </>
      ) : (
        <>
          <span className="mr-2">🛰️</span>
          Improve GPS Accuracy
        </>
      )}
    </button>
  );
};

// Helper function for icons (duplicated to avoid import issues)
function getAccuracyIcon(level: string): string {
  switch (level) {
    case 'excellent': return '🎯';
    case 'good': return '✅';
    case 'fair': return '⚠️';
    case 'poor': return '❌';
    default: return '📍';
  }
}
