/**
 * GPS Settings Component
 * Provides user controls for GPS accuracy preferences and optimization
 */

import React, { useState, useEffect } from 'react';
import { geolocationService } from '../services/geolocationService';
import {
  GPSPreferences as ImportedGPSPreferences,
  DEFAULT_GPS_PREFERENCES,
  loadGPSPreferences,
  saveGPSPreferences
} from '../utils/gpsPreferences';

interface GPSSettingsProps {
  onSettingsChange?: (settings: GPSPreferences) => void;
  className?: string;
}

export interface GPSPreferences {
  enableHighAccuracy: boolean;
  useMultipleReadings: boolean;
  enableCaching: boolean;
  enableReverseGeocoding: boolean;
  accuracyThreshold: number;
  timeout: number;
}

// Use the centralized default preferences
const DEFAULT_PREFERENCES: GPSPreferences = DEFAULT_GPS_PREFERENCES;

export const GPSSettings: React.FC<GPSSettingsProps> = ({
  onSettingsChange,
  className = ''
}) => {
  const [preferences, setPreferences] = useState<GPSPreferences>(DEFAULT_PREFERENCES);
  const [isExpanded, setIsExpanded] = useState(false);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'error' | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [autoSave, setAutoSave] = useState(true);

  useEffect(() => {
    // Load preferences using centralized function
    const loadedPreferences = loadGPSPreferences();
    setPreferences(loadedPreferences);
    console.log('GPS preferences loaded from localStorage:', loadedPreferences);

    // Load cache stats
    updateCacheStats();
  }, []);

  useEffect(() => {
    // Only auto-save if auto-save is enabled
    if (!autoSave) {
      setHasUnsavedChanges(true);
      return;
    }

    // Save preferences to localStorage whenever they change using centralized function
    setSaveStatus('saving');
    const success = saveGPSPreferences(preferences);

    if (success) {
      onSettingsChange?.(preferences);
      setSaveStatus('saved');
      setHasUnsavedChanges(false);

      // Clear save status after 2 seconds
      const timer = setTimeout(() => setSaveStatus(null), 2000);
      return () => clearTimeout(timer);
    } else {
      setSaveStatus('error');

      // Clear error status after 3 seconds
      const timer = setTimeout(() => setSaveStatus(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [preferences, onSettingsChange, autoSave]);

  const updateCacheStats = () => {
    const stats = geolocationService.getCacheStatus();
    setCacheStats(stats);
  };

  const handlePreferenceChange = (key: keyof GPSPreferences, value: any) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const clearCache = () => {
    geolocationService.clearCache();
    updateCacheStats();
    console.log('GPS cache cleared');
  };

  const resetToDefaults = () => {
    setPreferences(DEFAULT_PREFERENCES);
    console.log('GPS preferences reset to defaults');
  };

  const manualSave = () => {
    setSaveStatus('saving');
    const success = saveGPSPreferences(preferences);

    if (success) {
      onSettingsChange?.(preferences);
      setSaveStatus('saved');
      setHasUnsavedChanges(false);

      // Clear save status after 2 seconds
      setTimeout(() => setSaveStatus(null), 2000);
    } else {
      setSaveStatus('error');

      // Clear error status after 3 seconds
      setTimeout(() => setSaveStatus(null), 3000);
    }
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(preferences, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'hk-transit-hub-gps-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        const validatedPreferences = { ...DEFAULT_PREFERENCES, ...imported };
        setPreferences(validatedPreferences);
        console.log('GPS preferences imported successfully');
      } catch (error) {
        console.error('Failed to import GPS preferences:', error);
        alert('Failed to import settings. Please check the file format.');
      }
    };
    reader.readAsText(file);
    // Reset the input
    event.target.value = '';
  };

  const formatCacheAge = (age: number): string => {
    if (age < 60000) return `${Math.round(age / 1000)}s ago`;
    if (age < 3600000) return `${Math.round(age / 60000)}m ago`;
    return `${Math.round(age / 3600000)}h ago`;
  };

  return (
    <div className={`gps-settings ${className}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full p-4 text-left bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200"
      >
        <div className="flex items-center space-x-3">
          <span className="text-xl">🛰️</span>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-semibold text-gray-800 dark:text-gray-100">GPS & Location Settings</span>
              {saveStatus && (
                <span className={`text-xs px-2 py-1 rounded-full ${
                  saveStatus === 'saved' ? 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300' :
                  saveStatus === 'saving' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300' :
                  'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300'
                }`}>
                  {saveStatus === 'saved' ? '✓ Saved' : saveStatus === 'saving' ? '⏳ Saving...' : '✗ Error'}
                </span>
              )}
              {hasUnsavedChanges && (
                <span className="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300">
                  ⚠️ Unsaved
                </span>
              )}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Configure location accuracy and preferences</p>
          </div>
        </div>
        <svg
          className={`w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isExpanded && (
        <div className="gps-settings-content expanded mt-4 p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg space-y-6 shadow-sm gps-transition">
          {/* Accuracy Settings */}
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="mr-2">🎯</span>
              Accuracy Settings
            </h4>
            <div className="space-y-4">
              <label className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.enableHighAccuracy}
                  onChange={(e) => handlePreferenceChange('enableHighAccuracy', e.target.checked)}
                  className="mt-1 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
                />
                <div className="flex-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-200">High Accuracy Mode</span>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Uses GPS for better accuracy (may drain battery faster)</p>
                </div>
              </label>

              <label className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.useMultipleReadings}
                  onChange={(e) => handlePreferenceChange('useMultipleReadings', e.target.checked)}
                  className="mt-1 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
                />
                <div className="flex-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Multiple Readings</span>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Takes multiple GPS readings and averages them for better accuracy</p>
                </div>
              </label>

              <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">
                  Accuracy Threshold: <span className="text-blue-600 dark:text-blue-400 font-semibold">{preferences.accuracyThreshold}m</span>
                </label>
                <input
                  type="range"
                  min="10"
                  max="200"
                  step="10"
                  value={preferences.accuracyThreshold}
                  onChange={(e) => handlePreferenceChange('accuracyThreshold', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                  style={{
                    background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((preferences.accuracyThreshold - 10) / 190) * 100}%, #d1d5db ${((preferences.accuracyThreshold - 10) / 190) * 100}%, #d1d5db 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
                  <span className="flex items-center"><span className="mr-1">🎯</span>10m (High)</span>
                  <span className="flex items-center">200m (Low)<span className="ml-1">📍</span></span>
                </div>
              </div>

              <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">
                  Timeout: <span className="text-blue-600 dark:text-blue-400 font-semibold">{preferences.timeout / 1000}s</span>
                </label>
                <input
                  type="range"
                  min="5000"
                  max="30000"
                  step="1000"
                  value={preferences.timeout}
                  onChange={(e) => handlePreferenceChange('timeout', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                  style={{
                    background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((preferences.timeout - 5000) / 25000) * 100}%, #d1d5db ${((preferences.timeout - 5000) / 25000) * 100}%, #d1d5db 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
                  <span className="flex items-center"><span className="mr-1">⚡</span>5s (Fast)</span>
                  <span className="flex items-center">30s (Patient)<span className="ml-1">⏳</span></span>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Settings */}
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="mr-2">⚙️</span>
              Features
            </h4>
            <div className="space-y-4">
              <label className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.enableCaching}
                  onChange={(e) => handlePreferenceChange('enableCaching', e.target.checked)}
                  className="mt-1 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
                />
                <div className="flex-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Location Caching</span>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Cache recent locations for faster access and reduced battery usage</p>
                </div>
              </label>

              <label className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.enableReverseGeocoding}
                  onChange={(e) => handlePreferenceChange('enableReverseGeocoding', e.target.checked)}
                  className="mt-1 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
                />
                <div className="flex-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Address Lookup</span>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Convert coordinates to readable addresses using OpenStreetMap</p>
                </div>
              </label>
            </div>
          </div>

          {/* Cache Information */}
          {cacheStats && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                  <span className="mr-2">💾</span>
                  Cache Status
                </h4>
                <button
                  onClick={clearCache}
                  className="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 underline transition-colors"
                >
                  Clear Cache
                </button>
              </div>
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                {cacheStats.hasCache ? (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="text-green-500">📍</span>
                      <span className="text-gray-700 dark:text-gray-300">Location cached</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-blue-500">🎯</span>
                      <span className="text-gray-700 dark:text-gray-300">±{cacheStats.accuracy}m accuracy</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-yellow-500">⏰</span>
                      <span className="text-gray-700 dark:text-gray-300">{formatCacheAge(cacheStats.age)}</span>
                    </div>
                    {cacheStats.stats && (
                      <div className="flex items-center space-x-2">
                        <span className="text-purple-500">💾</span>
                        <span className="text-gray-700 dark:text-gray-300">{cacheStats.stats.totalEntries || 0} entries</span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                    <span>📍</span>
                    <span>No cached location available</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Save Settings */}
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="mr-2">💾</span>
              Save Settings
            </h4>
            <div className="space-y-3">
              {/* Auto-save toggle */}
              <label className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  checked={autoSave}
                  onChange={(e) => setAutoSave(e.target.checked)}
                  className="mt-1 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
                />
                <div className="flex-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Auto-save Changes</span>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Automatically save settings when changed</p>
                </div>
              </label>

              {/* Manual save button */}
              <button
                onClick={manualSave}
                disabled={!hasUnsavedChanges && autoSave}
                className={`w-full px-4 py-3 text-sm font-medium rounded-lg border transition-colors duration-200 flex items-center justify-center space-x-2 ${
                  hasUnsavedChanges || !autoSave
                    ? 'bg-blue-600 hover:bg-blue-700 border-blue-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                }`}
              >
                <span>💾</span>
                <span>
                  {saveStatus === 'saving' ? 'Saving...' :
                   hasUnsavedChanges ? 'Save Changes' :
                   'All Changes Saved'}
                </span>
              </button>
            </div>
          </div>

          {/* Quick Actions */}
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="mr-2">⚡</span>
              Quick Actions
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={resetToDefaults}
                className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <span>🔄</span>
                <span>Reset to Defaults</span>
              </button>
              <button
                onClick={updateCacheStats}
                className="px-4 py-2 text-sm bg-blue-100 dark:bg-blue-900/50 hover:bg-blue-200 dark:hover:bg-blue-900/70 rounded-lg border border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <span>🔄</span>
                <span>Refresh Stats</span>
              </button>
              <button
                onClick={exportSettings}
                className="px-4 py-2 text-sm bg-green-100 dark:bg-green-900/50 hover:bg-green-200 dark:hover:bg-green-900/70 rounded-lg border border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <span>📤</span>
                <span>Export Settings</span>
              </button>
              <label className="px-4 py-2 text-sm bg-purple-100 dark:bg-purple-900/50 hover:bg-purple-200 dark:hover:bg-purple-900/70 rounded-lg border border-purple-300 dark:border-purple-600 text-purple-700 dark:text-purple-300 transition-colors duration-200 flex items-center justify-center space-x-2 cursor-pointer">
                <span>📥</span>
                <span>Import Settings</span>
                <input
                  type="file"
                  accept=".json"
                  onChange={importSettings}
                  className="hidden"
                />
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
