/**
 * GPS Settings Component
 * Provides user controls for GPS accuracy preferences and optimization
 */

import React, { useState, useEffect } from 'react';
import { geolocationService } from '../services/geolocationService';

interface GPSSettingsProps {
  onSettingsChange?: (settings: GPSPreferences) => void;
  className?: string;
}

export interface GPSPreferences {
  enableHighAccuracy: boolean;
  useMultipleReadings: boolean;
  enableCaching: boolean;
  enableReverseGeocoding: boolean;
  accuracyThreshold: number;
  timeout: number;
}

const DEFAULT_PREFERENCES: GPSPreferences = {
  enableHighAccuracy: true,
  useMultipleReadings: true,
  enableCaching: true,
  enableReverseGeocoding: true,
  accuracyThreshold: 50,
  timeout: 10000
};

export const GPSSettings: React.FC<GPSSettingsProps> = ({
  onSettingsChange,
  className = ''
}) => {
  const [preferences, setPreferences] = useState<GPSPreferences>(DEFAULT_PREFERENCES);
  const [isExpanded, setIsExpanded] = useState(false);
  const [cacheStats, setCacheStats] = useState<any>(null);

  useEffect(() => {
    // Load preferences from localStorage
    const saved = localStorage.getItem('gps-preferences');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setPreferences({ ...DEFAULT_PREFERENCES, ...parsed });
      } catch (error) {
        console.warn('Failed to parse saved GPS preferences:', error);
      }
    }

    // Load cache stats
    updateCacheStats();
  }, []);

  useEffect(() => {
    // Save preferences to localStorage
    localStorage.setItem('gps-preferences', JSON.stringify(preferences));
    onSettingsChange?.(preferences);
  }, [preferences, onSettingsChange]);

  const updateCacheStats = () => {
    const stats = geolocationService.getCacheStatus();
    setCacheStats(stats);
  };

  const handlePreferenceChange = (key: keyof GPSPreferences, value: any) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const clearCache = () => {
    geolocationService.clearCache();
    updateCacheStats();
  };

  const formatCacheAge = (age: number): string => {
    if (age < 60000) return `${Math.round(age / 1000)}s ago`;
    if (age < 3600000) return `${Math.round(age / 60000)}m ago`;
    return `${Math.round(age / 3600000)}h ago`;
  };

  return (
    <div className={`gps-settings ${className}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <span className="text-lg">⚙️</span>
          <span className="font-medium text-gray-700">GPS Settings</span>
        </div>
        <svg
          className={`w-5 h-5 text-gray-500 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isExpanded && (
        <div className="mt-3 p-4 bg-white border border-gray-200 rounded-lg space-y-4">
          {/* Accuracy Settings */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Accuracy Settings</h4>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={preferences.enableHighAccuracy}
                  onChange={(e) => handlePreferenceChange('enableHighAccuracy', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">High Accuracy Mode</span>
                  <p className="text-xs text-gray-500">Uses GPS for better accuracy (may drain battery faster)</p>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={preferences.useMultipleReadings}
                  onChange={(e) => handlePreferenceChange('useMultipleReadings', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">Multiple Readings</span>
                  <p className="text-xs text-gray-500">Takes multiple GPS readings and averages them</p>
                </div>
              </label>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Accuracy Threshold: {preferences.accuracyThreshold}m
                </label>
                <input
                  type="range"
                  min="10"
                  max="200"
                  step="10"
                  value={preferences.accuracyThreshold}
                  onChange={(e) => handlePreferenceChange('accuracyThreshold', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>10m (High)</span>
                  <span>200m (Low)</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Timeout: {preferences.timeout / 1000}s
                </label>
                <input
                  type="range"
                  min="5000"
                  max="30000"
                  step="1000"
                  value={preferences.timeout}
                  onChange={(e) => handlePreferenceChange('timeout', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>5s (Fast)</span>
                  <span>30s (Patient)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Settings */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Features</h4>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={preferences.enableCaching}
                  onChange={(e) => handlePreferenceChange('enableCaching', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">Location Caching</span>
                  <p className="text-xs text-gray-500">Cache recent locations for faster access</p>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={preferences.enableReverseGeocoding}
                  onChange={(e) => handlePreferenceChange('enableReverseGeocoding', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">Address Lookup</span>
                  <p className="text-xs text-gray-500">Convert coordinates to readable addresses</p>
                </div>
              </label>
            </div>
          </div>

          {/* Cache Information */}
          {cacheStats && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">Cache Status</h4>
                <button
                  onClick={clearCache}
                  className="text-xs text-red-600 hover:text-red-800 underline"
                >
                  Clear Cache
                </button>
              </div>
              <div className="text-xs text-gray-600 space-y-1">
                {cacheStats.hasCache ? (
                  <>
                    <p>📍 Cached location available</p>
                    <p>🎯 Accuracy: ±{cacheStats.accuracy}m</p>
                    <p>⏰ Age: {formatCacheAge(cacheStats.age)}</p>
                    {cacheStats.stats && (
                      <p>💾 Cache: {cacheStats.stats.totalEntries || 0} entries</p>
                    )}
                  </>
                ) : (
                  <p>📍 No cached location</p>
                )}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Quick Actions</h4>
            <div className="flex space-x-2">
              <button
                onClick={() => setPreferences(DEFAULT_PREFERENCES)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border text-gray-700"
              >
                Reset to Defaults
              </button>
              <button
                onClick={updateCacheStats}
                className="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 rounded border text-blue-700"
              >
                Refresh Stats
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
