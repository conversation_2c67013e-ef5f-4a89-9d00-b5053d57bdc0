/**
 * Enhanced Geolocation Service for HK Transit Hub
 * Provides accurate, reliable location detection with progressive fallback strategies
 */

import { locationCache } from './locationCache';
import { reverseGeocodingService } from './reverseGeocodingService';
import { networkService } from './networkService';

export interface ReverseGeocodingOptions {
  language?: string;
  includeAddress?: boolean;
  timeout?: number;
}

export interface LocationCoordinates {
  lat: number;
  lng: number;
  accuracy?: number;
  timestamp?: number;
}

export interface LocationResult {
  coordinates: LocationCoordinates;
  displayName: string;
  source: 'gps' | 'network' | 'cache' | 'fallback';
  accuracy: 'high' | 'medium' | 'low';
}

export interface GeolocationError {
  code: number;
  message: string;
  type: 'permission_denied' | 'position_unavailable' | 'timeout' | 'not_supported' | 'network_error';
}

export interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  fallbackToLowAccuracy?: boolean;
  useCache?: boolean;
  requireUserInteraction?: boolean;
  enableReverseGeocoding?: boolean;
  geocodingOptions?: ReverseGeocodingOptions;
}

interface CachedLocation {
  coordinates: LocationCoordinates;
  displayName: string;
  timestamp: number;
  accuracy: number;
  source: string;
}

interface LocationReading {
  coordinates: LocationCoordinates;
  timestamp: number;
  accuracy: number;
}

class GeolocationService {
  private static instance: GeolocationService;
  private cachedLocation: CachedLocation | null = null;
  private isLocating = false;
  private lastLocationTime = 0;
  private locationReadings: LocationReading[] = [];
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly MIN_ACCURACY_THRESHOLD = 100; // meters
  private readonly HIGH_ACCURACY_THRESHOLD = 20; // meters
  private readonly MAX_READINGS_FOR_AVERAGING = 5;
  private readonly READING_TIMEOUT = 2000; // 2 seconds between readings
  private readonly HONG_KONG_BOUNDS = {
    north: 22.5,
    south: 22.1,
    east: 114.5,
    west: 113.8
  };

  private constructor() {}

  public static getInstance(): GeolocationService {
    if (!GeolocationService.instance) {
      GeolocationService.instance = new GeolocationService();
    }
    return GeolocationService.instance;
  }

  /**
   * Check if geolocation is supported by the browser
   */
  public isSupported(): boolean {
    return 'geolocation' in navigator && 'getCurrentPosition' in navigator.geolocation;
  }

  /**
   * Check geolocation permission status
   */
  public async checkPermission(): Promise<'granted' | 'denied' | 'prompt' | 'unsupported'> {
    if (!this.isSupported()) {
      return 'unsupported';
    }

    if ('permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' });

        // On some browsers (especially mobile Safari), the Permissions API
        // may return 'granted' but actual geolocation calls still fail
        // We'll still return the API result but handle this in the calling code
        return permission.state;
      } catch (error) {
        console.warn('Permission API not supported:', error);
      }
    }

    // Fallback: try to determine from previous attempts
    return 'prompt';
  }

  /**
   * Request geolocation permission with user guidance
   */
  public async requestPermission(): Promise<{
    granted: boolean;
    error?: GeolocationError;
    guidance?: string;
  }> {
    const permissionStatus = await this.checkPermission();

    if (permissionStatus === 'unsupported') {
      return {
        granted: false,
        error: this.createError(0, 'Geolocation is not supported', 'not_supported'),
        guidance: 'Your browser does not support location services. Please use a modern browser.'
      };
    }

    if (permissionStatus === 'denied') {
      return {
        granted: false,
        error: this.createError(1, 'Location permission denied', 'permission_denied'),
        guidance: 'Location access is blocked. Please enable it in your browser settings and refresh the page.'
      };
    }

    if (permissionStatus === 'granted') {
      return { granted: true };
    }

    // Permission is 'prompt' - try to get location to trigger permission request
    try {
      await this.getCurrentPositionPromise({
        enableHighAccuracy: false,
        timeout: 8000,
        maximumAge: 0
      });
      return { granted: true };
    } catch (error) {
      const geoError = error as GeolocationError;

      let guidance = '';
      switch (geoError.code) {
        case 1:
          guidance = 'Please allow location access when prompted, or enable it in your browser settings.';
          break;
        case 2:
          guidance = 'Location information is unavailable. Please check your device settings and try again.';
          break;
        case 3:
          guidance = 'Location request timed out. Please try again or check your connection.';
          break;
        default:
          guidance = 'An error occurred while requesting location access. Please try again.';
      }

      return {
        granted: false,
        error: geoError,
        guidance
      };
    }
  }

  /**
   * Quick permission check without triggering permission prompt
   */
  public async isPermissionGranted(): Promise<boolean> {
    const status = await this.checkPermission();
    return status === 'granted';
  }

  /**
   * Check if we're likely in Hong Kong based on coordinates
   */
  private isInHongKong(lat: number, lng: number): boolean {
    return lat >= this.HONG_KONG_BOUNDS.south && 
           lat <= this.HONG_KONG_BOUNDS.north &&
           lng >= this.HONG_KONG_BOUNDS.west && 
           lng <= this.HONG_KONG_BOUNDS.east;
  }

  /**
   * Get cached location if available and valid
   */
  private getCachedLocation(): CachedLocation | null {
    // First check advanced cache
    const cached = locationCache.getBest();
    if (cached) {
      return {
        coordinates: cached.coordinates,
        displayName: cached.displayName,
        timestamp: cached.timestamp,
        accuracy: cached.accuracy,
        source: cached.source
      };
    }

    // Fallback to simple cache
    if (!this.cachedLocation) return null;

    const now = Date.now();
    const age = now - this.cachedLocation.timestamp;

    if (age > this.CACHE_DURATION) {
      this.cachedLocation = null;
      return null;
    }

    return this.cachedLocation;
  }

  /**
   * Cache a location result
   */
  private cacheLocation(coordinates: LocationCoordinates, displayName: string, source: string): void {
    // Cache in advanced cache
    locationCache.set(coordinates, displayName, source);

    // Also cache in simple cache for backward compatibility
    this.cachedLocation = {
      coordinates,
      displayName,
      timestamp: Date.now(),
      accuracy: coordinates.accuracy || 0,
      source
    };
  }

  /**
   * Generate a display name for coordinates with optional reverse geocoding
   */
  private async generateDisplayName(
    coordinates: LocationCoordinates,
    enableReverseGeocoding: boolean = false,
    geocodingOptions?: ReverseGeocodingOptions
  ): Promise<string> {
    if (enableReverseGeocoding) {
      try {
        const result = await reverseGeocodingService.reverseGeocode(coordinates, geocodingOptions);
        return result.displayName;
      } catch (error) {
        console.warn('Reverse geocoding failed, using fallback:', error);
      }
    }

    const { lat, lng, accuracy } = coordinates;
    const accuracyText = accuracy ? ` (±${Math.round(accuracy)}m)` : '';
    return `Current Location (${lat.toFixed(4)}, ${lng.toFixed(4)})${accuracyText}`;
  }

  /**
   * Validate coordinates for reasonableness
   */
  private validateCoordinates(lat: number, lng: number): boolean {
    // Basic validation
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return false;
    }
    
    // Check if coordinates are not null island (0,0)
    if (lat === 0 && lng === 0) {
      return false;
    }
    
    return true;
  }

  /**
   * Get current location with progressive accuracy strategy
   */
  public async getCurrentLocation(options: GeolocationOptions = {}): Promise<LocationResult> {
    if (!this.isSupported()) {
      throw this.createError(0, 'Geolocation is not supported by this browser', 'not_supported');
    }

    // Check cache first if enabled
    if (options.useCache !== false) {
      const cached = this.getCachedLocation();
      if (cached) {
        return {
          coordinates: cached.coordinates,
          displayName: cached.displayName,
          source: 'cache',
          accuracy: this.getAccuracyLevel(cached.accuracy)
        };
      }
    }

    // Prevent multiple simultaneous requests
    if (this.isLocating) {
      throw this.createError(3, 'Location request already in progress', 'timeout');
    }

    this.isLocating = true;

    try {
      const result = await this.attemptLocationWithFallback(options);
      this.isLocating = false;
      return result;
    } catch (error) {
      this.isLocating = false;

      // If we get a permission denied error, provide detailed troubleshooting info
      const geoError = error as GeolocationError;
      if (geoError.code === 1) {
        console.warn('🚫 Location permission denied. This could be due to:');
        console.warn('1. Browser cached a previous "deny" decision');
        console.warn('2. Site permissions need to be reset');
        console.warn('3. Browser security settings blocking location');
        console.warn('');
        console.warn('🔧 To fix this:');
        console.warn('1. Click the lock/info icon in the address bar');
        console.warn('2. Reset permissions for this site');
        console.warn('3. Refresh the page and try again');
        console.warn('');
        console.warn('💡 Or run: resetLocationPermission() in console');

        // Enhance the error with troubleshooting info
        const enhancedError = this.createError(
          1,
          'Location access denied. Please reset site permissions and try again.',
          'permission_denied'
        );
        throw enhancedError;
      }

      throw error;
    }
  }

  /**
   * Attempt location detection with progressive fallback
   */
  private async attemptLocationWithFallback(options: GeolocationOptions): Promise<LocationResult> {
    // Get network-aware recommendations
    const networkOptions = networkService.getGeolocationOptions();
    const isFastConnection = networkService.isFastConnection();

    // Detect mobile Safari which has known permission issues
    const isMobileSafari = navigator.userAgent.includes('Safari') &&
                          navigator.userAgent.includes('Mobile') &&
                          !navigator.userAgent.includes('Chrome');

    const strategies = [
      // Strategy 1: Conservative start for mobile Safari, high accuracy for others
      ...(isFastConnection && !networkService.isDataSavingEnabled() && !isMobileSafari ? [{
        enableHighAccuracy: true,
        timeout: Math.min(8000, networkOptions.timeout * 0.6),
        maximumAge: 60000,
        description: 'high-accuracy-fast'
      }] : []),

      // Strategy 2: Mobile Safari friendly approach
      ...(isMobileSafari ? [{
        enableHighAccuracy: false,
        timeout: 15000,
        maximumAge: 120000,
        description: 'mobile-safari-friendly'
      }] : []),

      // Strategy 3: Network-optimized accuracy
      {
        enableHighAccuracy: networkOptions.enableHighAccuracy && !isMobileSafari,
        timeout: networkOptions.timeout,
        maximumAge: networkOptions.maximumAge,
        description: 'network-optimized'
      },

      // Strategy 4: Conservative fallback
      {
        enableHighAccuracy: false,
        timeout: Math.max(15000, networkOptions.timeout * 1.5),
        maximumAge: 300000,
        description: 'conservative-fallback'
      },

      // Strategy 5: Last resort (offline-friendly)
      {
        enableHighAccuracy: false,
        timeout: 30000,
        maximumAge: 600000, // 10 minutes
        description: 'last-resort'
      }
    ];

    let lastError: GeolocationError | null = null;

    for (let i = 0; i < strategies.length; i++) {
      const strategy = strategies[i];
      try {
        console.log(`Attempting geolocation with ${strategy.description} strategy (${i + 1}/${strategies.length})`);

        let coordinates: LocationCoordinates;

        // For high-accuracy strategies, try multiple readings for better accuracy
        if (strategy.enableHighAccuracy && isFastConnection) {
          try {
            coordinates = await this.getMultipleReadings(strategy, 3);
          } catch (multiError) {
            console.warn('Multiple readings failed, falling back to single reading:', multiError);
            const position = await this.getCurrentPositionPromise(strategy);
            const { latitude, longitude, accuracy } = position.coords;
            coordinates = {
              lat: latitude,
              lng: longitude,
              accuracy: accuracy || undefined,
              timestamp: Date.now()
            };
          }
        } else {
          // Single reading for network-based or low-accuracy strategies
          const position = await this.getCurrentPositionPromise(strategy);
          const { latitude, longitude, accuracy } = position.coords;
          coordinates = {
            lat: latitude,
            lng: longitude,
            accuracy: accuracy || undefined,
            timestamp: Date.now()
          };
        }

        // Validate coordinates
        if (!this.validateCoordinates(coordinates.lat, coordinates.lng)) {
          console.warn(`Invalid coordinates received: ${coordinates.lat}, ${coordinates.lng}`);
          continue;
        }

        // Check accuracy requirements for this strategy
        const accuracy = coordinates.accuracy || 0;
        if (accuracy > 0 && !this.isAccuracyAcceptable(accuracy, strategy.description)) {
          console.warn(`Accuracy ${accuracy}m not acceptable for ${strategy.description} strategy, trying next`);
          continue;
        }

        // Check if we're in Hong Kong (optional warning)
        if (!this.isInHongKong(coordinates.lat, coordinates.lng)) {
          console.warn(`Location appears to be outside Hong Kong: ${coordinates.lat}, ${coordinates.lng}`);
        }

        const displayName = await this.generateDisplayName(
          coordinates,
          options.enableReverseGeocoding,
          options.geocodingOptions
        );
        const source = strategy.enableHighAccuracy ? 'gps' : 'network';
        const accuracyLevel = this.getAccuracyLevel(accuracy);

        // Cache the result
        this.cacheLocation(coordinates, displayName, source);

        console.log(`Location successfully obtained using ${strategy.description} strategy:`, {
          coordinates,
          accuracy: accuracyLevel,
          actualAccuracy: `${accuracy}m`,
          source
        });

        return {
          coordinates,
          displayName,
          source,
          accuracy: accuracyLevel
        };

      } catch (error) {
        const geoError = error as GeolocationError;
        console.warn(`${strategy.description} strategy failed:`, geoError);
        lastError = geoError;

        // If permission denied, don't try other strategies
        if (geoError.code === 1) {
          console.error('Permission denied - stopping all location attempts');
          break;
        }

        // If this is not the last strategy, continue to next one
        if (i < strategies.length - 1) {
          console.log(`Trying next strategy...`);
        }
      }
    }

    // All strategies failed
    throw lastError || this.createError(2, 'All location strategies failed', 'position_unavailable');
  }

  /**
   * Convert native geolocation to Promise
   */
  private getCurrentPositionPromise(options: PositionOptions): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        resolve,
        (error) => reject(this.createError(error.code, error.message, this.getErrorType(error.code))),
        options
      );
    });
  }

  /**
   * Add a location reading for averaging
   */
  private addLocationReading(coordinates: LocationCoordinates, accuracy: number): void {
    const reading: LocationReading = {
      coordinates,
      timestamp: Date.now(),
      accuracy
    };

    this.locationReadings.push(reading);
    this.lastLocationTime = Date.now();

    // Keep only recent readings
    const cutoff = Date.now() - (this.READING_TIMEOUT * this.MAX_READINGS_FOR_AVERAGING);
    this.locationReadings = this.locationReadings.filter(r => r.timestamp > cutoff);

    // Limit number of readings
    if (this.locationReadings.length > this.MAX_READINGS_FOR_AVERAGING) {
      this.locationReadings = this.locationReadings.slice(-this.MAX_READINGS_FOR_AVERAGING);
    }
  }

  /**
   * Calculate weighted average of location readings
   */
  private calculateAverageLocation(): LocationCoordinates | null {
    if (this.locationReadings.length === 0) return null;

    // Filter out readings with poor accuracy if we have better ones
    let validReadings = this.locationReadings;
    const goodReadings = this.locationReadings.filter(r => r.accuracy <= this.MIN_ACCURACY_THRESHOLD);
    if (goodReadings.length > 0) {
      validReadings = goodReadings;
    }

    if (validReadings.length === 1) {
      return validReadings[0].coordinates;
    }

    // Calculate weighted average based on accuracy (lower accuracy = higher weight)
    let totalWeight = 0;
    let weightedLat = 0;
    let weightedLng = 0;
    let bestAccuracy = Infinity;

    for (const reading of validReadings) {
      // Weight is inverse of accuracy (better accuracy = higher weight)
      const weight = reading.accuracy > 0 ? 1 / reading.accuracy : 1;
      totalWeight += weight;
      weightedLat += reading.coordinates.lat * weight;
      weightedLng += reading.coordinates.lng * weight;
      bestAccuracy = Math.min(bestAccuracy, reading.accuracy);
    }

    if (totalWeight === 0) return null;

    return {
      lat: weightedLat / totalWeight,
      lng: weightedLng / totalWeight,
      accuracy: bestAccuracy,
      timestamp: Date.now()
    };
  }

  /**
   * Check if location reading meets accuracy requirements
   */
  private isAccuracyAcceptable(accuracy: number, strategy: string): boolean {
    // For high-accuracy strategies, be more strict
    if (strategy.includes('high-accuracy')) {
      return accuracy <= this.HIGH_ACCURACY_THRESHOLD;
    }

    // For other strategies, use general threshold
    return accuracy <= this.MIN_ACCURACY_THRESHOLD;
  }

  /**
   * Get multiple readings and return the best averaged result
   */
  private async getMultipleReadings(options: PositionOptions, maxReadings: number = 3): Promise<LocationCoordinates> {
    this.locationReadings = []; // Clear previous readings

    const readings: LocationCoordinates[] = [];
    const errors: GeolocationError[] = [];

    for (let i = 0; i < maxReadings; i++) {
      try {
        const position = await this.getCurrentPositionPromise(options);
        const { latitude, longitude, accuracy } = position.coords;

        const coordinates: LocationCoordinates = {
          lat: latitude,
          lng: longitude,
          accuracy: accuracy || undefined,
          timestamp: Date.now()
        };

        this.addLocationReading(coordinates, accuracy || 0);
        readings.push(coordinates);

        // If we get a very accurate reading, we can stop early
        if (accuracy && accuracy <= this.HIGH_ACCURACY_THRESHOLD) {
          console.log(`High accuracy reading obtained (${accuracy}m), stopping early`);
          break;
        }

        // Small delay between readings
        if (i < maxReadings - 1) {
          await new Promise(resolve => setTimeout(resolve, this.READING_TIMEOUT));
        }

      } catch (error) {
        errors.push(error as GeolocationError);
        console.warn(`Reading ${i + 1} failed:`, error);
      }
    }

    if (readings.length === 0) {
      throw errors[0] || this.createError(2, 'No valid readings obtained', 'position_unavailable');
    }

    // Return averaged location or best single reading
    const averaged = this.calculateAverageLocation();
    if (averaged) {
      console.log(`Averaged ${readings.length} readings, final accuracy: ${averaged.accuracy}m`);
      return averaged;
    }

    // Fallback to best single reading
    const bestReading = readings.reduce((best, current) => {
      const bestAcc = best.accuracy || Infinity;
      const currentAcc = current.accuracy || Infinity;
      return currentAcc < bestAcc ? current : best;
    });

    console.log(`Using best single reading with accuracy: ${bestReading.accuracy}m`);
    return bestReading;
  }

  /**
   * Determine accuracy level based on accuracy value
   */
  private getAccuracyLevel(accuracy: number): 'high' | 'medium' | 'low' {
    if (accuracy <= this.HIGH_ACCURACY_THRESHOLD) return 'high';
    if (accuracy <= this.MIN_ACCURACY_THRESHOLD) return 'medium';
    return 'low';
  }

  /**
   * Create standardized error object
   */
  private createError(code: number, message: string, type: GeolocationError['type']): GeolocationError {
    return { code, message, type };
  }

  /**
   * Map error codes to types
   */
  private getErrorType(code: number): GeolocationError['type'] {
    switch (code) {
      case 1: return 'permission_denied';
      case 2: return 'position_unavailable';
      case 3: return 'timeout';
      default: return 'position_unavailable';
    }
  }

  /**
   * Clear cached location
   */
  public clearCache(): void {
    this.cachedLocation = null;
    locationCache.clear();
  }

  /**
   * Get cache status
   */
  public getCacheStatus(): { hasCache: boolean; age?: number; accuracy?: number; stats?: any } {
    const cached = this.getCachedLocation();
    const cacheStats = locationCache.getStats();

    if (!cached) {
      return { hasCache: false, stats: cacheStats };
    }

    return {
      hasCache: true,
      age: Date.now() - cached.timestamp,
      accuracy: cached.accuracy,
      stats: cacheStats
    };
  }

  /**
   * Get nearby cached location
   */
  public getNearbyLocation(coordinates: LocationCoordinates, radiusMeters: number = 50): LocationResult | null {
    const nearby = locationCache.hasNearby(coordinates, radiusMeters);
    if (!nearby) return null;

    return {
      coordinates: nearby.coordinates,
      displayName: nearby.displayName,
      source: 'cache',
      accuracy: this.getAccuracyLevel(nearby.accuracy)
    };
  }

  /**
   * Get GPS accuracy feedback and suggestions
   */
  public getAccuracyFeedback(accuracy: number): {
    level: 'excellent' | 'good' | 'fair' | 'poor';
    message: string;
    suggestions: string[];
  } {
    if (accuracy <= 5) {
      return {
        level: 'excellent',
        message: 'Excellent GPS accuracy',
        suggestions: []
      };
    } else if (accuracy <= 20) {
      return {
        level: 'good',
        message: 'Good GPS accuracy',
        suggestions: []
      };
    } else if (accuracy <= 100) {
      return {
        level: 'fair',
        message: 'Fair GPS accuracy',
        suggestions: [
          'Move to an area with better sky visibility',
          'Wait a moment for GPS to improve'
        ]
      };
    } else {
      return {
        level: 'poor',
        message: 'Poor GPS accuracy',
        suggestions: [
          'Move outdoors or near a window',
          'Ensure location services are enabled',
          'Check if GPS is blocked by buildings',
          'Try refreshing your location'
        ]
      };
    }
  }

  /**
   * Monitor GPS accuracy over time
   */
  public startAccuracyMonitoring(callback: (accuracy: number, feedback: any) => void): () => void {
    let watchId: number | null = null;
    let isMonitoring = true;

    const startWatching = () => {
      if (!this.isSupported() || !isMonitoring) return;

      watchId = navigator.geolocation.watchPosition(
        (position) => {
          if (!isMonitoring) return;

          const accuracy = position.coords.accuracy || 0;
          const feedback = this.getAccuracyFeedback(accuracy);
          callback(accuracy, feedback);
        },
        (error) => {
          console.warn('GPS monitoring error:', error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 5000
        }
      );
    };

    startWatching();

    // Return cleanup function
    return () => {
      isMonitoring = false;
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
        watchId = null;
      }
    };
  }

  /**
   * Warm up GPS for better initial accuracy
   */
  public async warmUpGPS(): Promise<void> {
    if (!this.isSupported()) return;

    console.log('Warming up GPS...');

    try {
      // Make a quick low-accuracy request to start GPS
      await this.getCurrentPositionPromise({
        enableHighAccuracy: false,
        timeout: 5000,
        maximumAge: 0
      });

      // Small delay to let GPS stabilize
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('GPS warm-up completed');
    } catch (error) {
      console.warn('GPS warm-up failed:', error);
    }
  }

  /**
   * Enhanced location detection with automatic retries and accuracy improvement
   */
  public async getCurrentLocationWithRetries(
    options: GeolocationOptions = {},
    maxRetries: number = 3
  ): Promise<LocationResult> {
    let lastError: GeolocationError | null = null;
    let bestResult: LocationResult | null = null;

    // Warm up GPS if this is a high-accuracy request
    if (options.enableHighAccuracy !== false) {
      try {
        await this.warmUpGPS();
      } catch (error) {
        console.warn('GPS warm-up failed, continuing anyway:', error);
      }
    }

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Location attempt ${attempt}/${maxRetries}`);

        // Adjust options based on attempt number
        const attemptOptions = { ...options };
        if (attempt > 1) {
          // Increase timeout for subsequent attempts
          attemptOptions.timeout = (attemptOptions.timeout || 10000) * attempt;
          // Allow older cached results
          attemptOptions.maximumAge = Math.max(
            attemptOptions.maximumAge || 60000,
            60000 * attempt
          );
        }

        const result = await this.getCurrentLocation(attemptOptions);

        // Check if this result is better than previous attempts
        if (!bestResult || this.isResultBetter(result, bestResult)) {
          bestResult = result;
        }

        // If we got a high-accuracy result, we can stop
        const accuracy = result.coordinates.accuracy || 0;
        if (accuracy > 0 && accuracy <= this.HIGH_ACCURACY_THRESHOLD) {
          console.log(`High accuracy achieved (${accuracy}m), stopping retries`);
          return result;
        }

        // If this is not the last attempt and accuracy is poor, continue
        if (attempt < maxRetries && accuracy > this.MIN_ACCURACY_THRESHOLD) {
          console.log(`Accuracy ${accuracy}m not satisfactory, retrying...`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }

        return result;

      } catch (error) {
        lastError = error as GeolocationError;
        console.warn(`Attempt ${attempt} failed:`, lastError);

        // If permission denied, don't retry
        if (lastError.code === 1) {
          throw lastError;
        }

        // If this is the last attempt, throw the error or return best result
        if (attempt === maxRetries) {
          if (bestResult) {
            console.log('Returning best result from previous attempts');
            return bestResult;
          }
          throw lastError;
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError || this.createError(2, 'All retry attempts failed', 'position_unavailable');
  }

  /**
   * Compare two location results to determine which is better
   */
  private isResultBetter(newResult: LocationResult, currentBest: LocationResult): boolean {
    const newAccuracy = newResult.coordinates.accuracy || Infinity;
    const bestAccuracy = currentBest.coordinates.accuracy || Infinity;

    // Prefer higher accuracy (lower accuracy value)
    if (newAccuracy < bestAccuracy) return true;
    if (newAccuracy > bestAccuracy) return false;

    // If accuracy is similar, prefer GPS over network
    if (newResult.source === 'gps' && currentBest.source === 'network') return true;
    if (newResult.source === 'network' && currentBest.source === 'gps') return false;

    // If everything else is equal, prefer newer result
    const newTime = newResult.coordinates.timestamp || 0;
    const bestTime = currentBest.coordinates.timestamp || 0;
    return newTime > bestTime;
  }

  /**
   * Get location with progressive accuracy improvement
   */
  public async getCurrentLocationProgressive(
    options: GeolocationOptions = {},
    onProgress?: (result: LocationResult, isImproving: boolean) => void
  ): Promise<LocationResult> {
    let bestResult: LocationResult | null = null;
    let improvementAttempts = 0;
    const maxImprovementAttempts = 3;

    // Start with a quick, low-accuracy reading
    try {
      const quickResult = await this.getCurrentLocation({
        ...options,
        enableHighAccuracy: false,
        timeout: 5000,
        maximumAge: 30000
      });

      bestResult = quickResult;
      onProgress?.(quickResult, false);

      // If accuracy is already good enough, return it
      const accuracy = quickResult.coordinates.accuracy || 0;
      if (accuracy > 0 && accuracy <= this.HIGH_ACCURACY_THRESHOLD) {
        return quickResult;
      }
    } catch (error) {
      console.warn('Quick location failed, trying high accuracy:', error);
    }

    // Try to improve accuracy with high-accuracy readings
    while (improvementAttempts < maxImprovementAttempts) {
      try {
        const improvedResult = await this.getCurrentLocation({
          ...options,
          enableHighAccuracy: true,
          timeout: 8000 + (improvementAttempts * 2000),
          maximumAge: 0
        });

        const isImprovement = !bestResult || this.isResultBetter(improvedResult, bestResult);

        if (isImprovement) {
          bestResult = improvedResult;
          onProgress?.(improvedResult, true);

          // Check if we've reached satisfactory accuracy
          const accuracy = improvedResult.coordinates.accuracy || 0;
          if (accuracy > 0 && accuracy <= this.HIGH_ACCURACY_THRESHOLD) {
            console.log(`Satisfactory accuracy achieved: ${accuracy}m`);
            break;
          }
        }

        improvementAttempts++;

        // Small delay between improvement attempts
        if (improvementAttempts < maxImprovementAttempts) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        console.warn(`Improvement attempt ${improvementAttempts + 1} failed:`, error);
        improvementAttempts++;
      }
    }

    if (!bestResult) {
      throw this.createError(2, 'No location could be obtained', 'position_unavailable');
    }

    return bestResult;
  }
}

// Export singleton instance
export const geolocationService = GeolocationService.getInstance();
