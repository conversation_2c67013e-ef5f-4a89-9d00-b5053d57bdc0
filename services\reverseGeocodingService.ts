/**
 * Reverse Geocoding Service for HK Transit Hub
 * Provides location name resolution with multiple fallback strategies
 */

import { LocationCoordinates, ReverseGeocodingOptions } from './geolocationService';
import { cacheManager } from './cacheManager';

export interface ReverseGeocodingResult {
  displayName: string;
  address?: string;
  district?: string;
  region?: string;
  source: 'nominatim' | 'browser' | 'cache' | 'fallback';
  confidence: number;
}

class ReverseGeocodingService {
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly REQUEST_TIMEOUT = 5000; // 5 seconds
  private readonly HONG_KONG_BOUNDS = {
    north: 22.5,
    south: 22.1,
    east: 114.5,
    west: 113.8
  };

  /**
   * Generate cache key for coordinates
   */
  private getCacheKey(coordinates: LocationCoordinates): string {
    // Round to ~100m precision for caching
    const lat = Math.round(coordinates.lat * 1000) / 1000;
    const lng = Math.round(coordinates.lng * 1000) / 1000;
    return `geocode:${lat},${lng}`;
  }

  /**
   * Check if coordinates are in Hong Kong
   */
  private isInHongKong(coordinates: LocationCoordinates): boolean {
    const { lat, lng } = coordinates;
    return lat >= this.HONG_KONG_BOUNDS.south && 
           lat <= this.HONG_KONG_BOUNDS.north &&
           lng >= this.HONG_KONG_BOUNDS.west && 
           lng <= this.HONG_KONG_BOUNDS.east;
  }

  /**
   * Reverse geocode using Nominatim (OpenStreetMap)
   */
  private async reverseGeocodeNominatim(
    coordinates: LocationCoordinates, 
    options: ReverseGeocodingOptions = {}
  ): Promise<ReverseGeocodingResult> {
    const { lat, lng } = coordinates;
    const language = options.language || 'en';
    
    const url = new URL('https://nominatim.openstreetmap.org/reverse');
    url.searchParams.set('format', 'json');
    url.searchParams.set('lat', lat.toString());
    url.searchParams.set('lon', lng.toString());
    url.searchParams.set('zoom', '18');
    url.searchParams.set('addressdetails', '1');
    url.searchParams.set('accept-language', language);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || this.REQUEST_TIMEOUT);

    try {
      const response = await fetch(url.toString(), {
        signal: controller.signal,
        headers: {
          'User-Agent': 'HK Transit Hub (https://github.com/Alex2003763/hk-transit-hub)'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Nominatim API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data || data.error) {
        throw new Error(data?.error || 'No results from Nominatim');
      }

      // Extract meaningful location information
      const address = data.address || {};
      const displayName = this.formatDisplayName(data.display_name, address);
      
      return {
        displayName,
        address: data.display_name,
        district: address.suburb || address.neighbourhood || address.quarter,
        region: address.state_district || address.state,
        source: 'nominatim',
        confidence: 0.8
      };

    } catch (error) {
      clearTimeout(timeoutId);
      console.warn('Nominatim reverse geocoding failed:', error);
      throw error;
    }
  }

  /**
   * Format display name for Hong Kong locations
   */
  private formatDisplayName(fullAddress: string, addressComponents: any): string {
    if (!fullAddress) return 'Unknown Location';

    // For Hong Kong locations, try to extract meaningful parts
    if (this.isInHongKong({ lat: 0, lng: 0 })) { // We know it's HK from context
      const parts = fullAddress.split(',').map(p => p.trim());
      
      // Try to find building/street and district
      const building = addressComponents.building || addressComponents.house_number;
      const road = addressComponents.road;
      const suburb = addressComponents.suburb || addressComponents.neighbourhood;
      const district = addressComponents.state_district;

      if (building && road) {
        return suburb ? `${building} ${road}, ${suburb}` : `${building} ${road}`;
      } else if (road) {
        return suburb ? `${road}, ${suburb}` : road;
      } else if (suburb) {
        return district ? `${suburb}, ${district}` : suburb;
      }
    }

    // Fallback: use first 2-3 meaningful parts
    const parts = fullAddress.split(',').map(p => p.trim()).filter(p => p.length > 0);
    if (parts.length >= 3) {
      return parts.slice(0, 3).join(', ');
    } else if (parts.length >= 2) {
      return parts.slice(0, 2).join(', ');
    }
    
    return parts[0] || 'Unknown Location';
  }

  /**
   * Generate fallback location name
   */
  private generateFallbackName(coordinates: LocationCoordinates): ReverseGeocodingResult {
    const { lat, lng, accuracy } = coordinates;
    
    let displayName = `Location (${lat.toFixed(4)}, ${lng.toFixed(4)})`;
    
    if (accuracy) {
      displayName += ` ±${Math.round(accuracy)}m`;
    }

    // Add Hong Kong context if in bounds
    if (this.isInHongKong(coordinates)) {
      displayName = `Hong Kong ${displayName}`;
    }

    return {
      displayName,
      source: 'fallback',
      confidence: 0.1
    };
  }

  /**
   * Main reverse geocoding method with fallback strategies
   */
  async reverseGeocode(
    coordinates: LocationCoordinates, 
    options: ReverseGeocodingOptions = {}
  ): Promise<ReverseGeocodingResult> {
    const cacheKey = this.getCacheKey(coordinates);

    // Check cache first
    const cached = cacheManager.get<ReverseGeocodingResult>(cacheKey);
    if (cached) {
      console.log('Reverse geocoding cache hit');
      return { ...cached, source: 'cache' };
    }

    // Try Nominatim first
    try {
      const result = await this.reverseGeocodeNominatim(coordinates, options);
      
      // Cache successful result
      cacheManager.set(cacheKey, result, {
        maxAge: this.CACHE_DURATION,
        maxSize: 200
      });
      
      return result;
    } catch (error) {
      console.warn('Nominatim reverse geocoding failed, using fallback:', error);
    }

    // Fallback to coordinate-based name
    const fallback = this.generateFallbackName(coordinates);
    
    // Cache fallback result for shorter duration
    cacheManager.set(cacheKey, fallback, {
      maxAge: 60 * 60 * 1000, // 1 hour
      maxSize: 200
    });
    
    return fallback;
  }

  /**
   * Batch reverse geocode multiple locations
   */
  async reverseGeocodeBatch(
    locations: LocationCoordinates[], 
    options: ReverseGeocodingOptions = {}
  ): Promise<ReverseGeocodingResult[]> {
    const results: ReverseGeocodingResult[] = [];
    
    // Process in small batches to avoid overwhelming the service
    const batchSize = 5;
    for (let i = 0; i < locations.length; i += batchSize) {
      const batch = locations.slice(i, i + batchSize);
      const batchPromises = batch.map(location => 
        this.reverseGeocode(location, options).catch(error => {
          console.warn('Batch reverse geocoding failed for location:', location, error);
          return this.generateFallbackName(location);
        })
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Small delay between batches to be respectful to the service
      if (i + batchSize < locations.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return results;
  }

  /**
   * Clear reverse geocoding cache
   */
  clearCache(): void {
    // Clear all geocoding cache entries
    const stats = cacheManager.getStats();
    console.log('Clearing reverse geocoding cache, current stats:', stats);
    
    // Note: This clears the entire cache, not just geocoding entries
    // In a real implementation, you might want a more targeted approach
    cacheManager.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return cacheManager.getStats();
  }

  /**
   * Validate coordinates
   */
  private validateCoordinates(coordinates: LocationCoordinates): boolean {
    const { lat, lng } = coordinates;
    return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
  }

  /**
   * Get location suggestions based on partial coordinates
   */
  async getLocationSuggestions(
    partialCoordinates: Partial<LocationCoordinates>,
    options: ReverseGeocodingOptions = {}
  ): Promise<ReverseGeocodingResult[]> {
    // This is a placeholder for future implementation
    // Could be used for autocomplete or nearby location suggestions
    return [];
  }
}

// Export singleton instance
export const reverseGeocodingService = new ReverseGeocodingService();
