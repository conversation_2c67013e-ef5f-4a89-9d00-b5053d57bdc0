/**
 * GPS Optimization Utilities
 * Provides helper functions for optimizing GPS accuracy and performance
 */

import { geolocationService, LocationCoordinates, LocationResult } from '../services/geolocationService';
import { networkService } from '../services/networkService';

export interface GPSOptimizationOptions {
  prioritizeAccuracy?: boolean;
  prioritizeSpeed?: boolean;
  adaptToNetwork?: boolean;
  enableBatteryOptimization?: boolean;
  maxRetries?: number;
  accuracyThreshold?: number;
}

export interface GPSOptimizationResult {
  location: LocationResult;
  optimizationApplied: string[];
  performanceMetrics: {
    timeToFirstFix: number;
    finalAccuracy: number;
    retriesUsed: number;
    strategyUsed: string;
  };
}

/**
 * Get optimized GPS location based on current conditions and preferences
 */
export async function getOptimizedLocation(
  options: GPSOptimizationOptions = {}
): Promise<GPSOptimizationResult> {
  const startTime = Date.now();
  const optimizations: string[] = [];
  
  // Analyze current conditions
  const networkInfo = networkService.getConnectionInfo();
  const isFastConnection = networkService.isFastConnection();
  const isDataSavingEnabled = networkService.isDataSavingEnabled();
  
  // Determine optimal strategy
  let strategy = 'balanced';
  let gpsOptions: any = {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 60000,
    useCache: true,
    enableReverseGeocoding: true
  };

  // Apply network-based optimizations
  if (options.adaptToNetwork !== false) {
    if (!isFastConnection || isDataSavingEnabled) {
      gpsOptions.enableHighAccuracy = false;
      gpsOptions.timeout = 15000;
      gpsOptions.maximumAge = 300000; // 5 minutes
      strategy = 'network-optimized';
      optimizations.push('Network-optimized settings applied');
    }
  }

  // Apply accuracy prioritization
  if (options.prioritizeAccuracy) {
    gpsOptions.enableHighAccuracy = true;
    gpsOptions.timeout = Math.max(gpsOptions.timeout, 15000);
    gpsOptions.maximumAge = Math.min(gpsOptions.maximumAge, 30000);
    strategy = 'accuracy-prioritized';
    optimizations.push('High accuracy mode enabled');
  }

  // Apply speed prioritization
  if (options.prioritizeSpeed) {
    gpsOptions.enableHighAccuracy = false;
    gpsOptions.timeout = Math.min(gpsOptions.timeout, 5000);
    gpsOptions.maximumAge = Math.max(gpsOptions.maximumAge, 300000);
    strategy = 'speed-prioritized';
    optimizations.push('Fast location mode enabled');
  }

  // Apply battery optimization
  if (options.enableBatteryOptimization) {
    gpsOptions.enableHighAccuracy = false;
    gpsOptions.maximumAge = Math.max(gpsOptions.maximumAge, 600000); // 10 minutes
    strategy = 'battery-optimized';
    optimizations.push('Battery optimization enabled');
  }

  // Apply accuracy threshold
  if (options.accuracyThreshold) {
    optimizations.push(`Accuracy threshold set to ${options.accuracyThreshold}m`);
  }

  let location: LocationResult;
  let retriesUsed = 0;
  const maxRetries = options.maxRetries || 3;

  // Try to get location with retries if needed
  try {
    if (maxRetries > 1) {
      location = await geolocationService.getCurrentLocationWithRetries(gpsOptions, maxRetries);
      retriesUsed = maxRetries; // Approximate, actual retries are handled internally
    } else {
      location = await geolocationService.getCurrentLocation(gpsOptions);
    }
  } catch (error) {
    // Fallback to cached location if available
    const cached = geolocationService.getCacheStatus();
    if (cached.hasCache) {
      optimizations.push('Fallback to cached location');
      // This is a simplified fallback - in practice, you'd get the actual cached location
      throw error; // For now, just re-throw
    }
    throw error;
  }

  const timeToFirstFix = Date.now() - startTime;
  const finalAccuracy = location.coordinates.accuracy || 0;

  // Apply post-processing optimizations
  if (options.accuracyThreshold && finalAccuracy > options.accuracyThreshold) {
    optimizations.push(`Accuracy warning: ${finalAccuracy}m exceeds threshold of ${options.accuracyThreshold}m`);
  }

  return {
    location,
    optimizationApplied: optimizations,
    performanceMetrics: {
      timeToFirstFix,
      finalAccuracy,
      retriesUsed,
      strategyUsed: strategy
    }
  };
}

/**
 * Get GPS recommendations based on current conditions
 */
export function getGPSRecommendations(): {
  recommendations: string[];
  warnings: string[];
  optimizations: string[];
} {
  const recommendations: string[] = [];
  const warnings: string[] = [];
  const optimizations: string[] = [];

  const networkInfo = networkService.getConnectionInfo();
  const isFastConnection = networkService.isFastConnection();
  const isOnline = networkService.isOnline();

  // Network-based recommendations
  if (!isOnline) {
    warnings.push('Device is offline - GPS accuracy may be limited');
    recommendations.push('Connect to internet for better location services');
  } else if (!isFastConnection) {
    recommendations.push('Slow connection detected - consider using battery-optimized mode');
    optimizations.push('Use network-optimized GPS settings');
  }

  // Device-based recommendations
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = /mobile|android|iphone|ipad/.test(userAgent);
  const isIOS = /iphone|ipad/.test(userAgent);

  if (isMobile) {
    recommendations.push('Move to an area with clear sky view for better GPS accuracy');
    if (isIOS) {
      recommendations.push('Ensure Location Services are enabled in iOS Settings');
    }
  }

  // Time-based recommendations
  const hour = new Date().getHours();
  if (hour >= 22 || hour <= 6) {
    optimizations.push('Night time detected - consider enabling battery optimization');
  }

  // Cache-based recommendations
  const cacheStatus = geolocationService.getCacheStatus();
  if (cacheStatus.hasCache) {
    const age = cacheStatus.age || 0;
    if (age > 300000) { // 5 minutes
      recommendations.push('Cached location is old - consider refreshing');
    } else {
      optimizations.push('Recent cached location available for faster access');
    }
  }

  return {
    recommendations,
    warnings,
    optimizations
  };
}

/**
 * Analyze GPS performance and provide insights
 */
export function analyzeGPSPerformance(results: GPSOptimizationResult[]): {
  averageAccuracy: number;
  averageTimeToFix: number;
  successRate: number;
  recommendations: string[];
} {
  if (results.length === 0) {
    return {
      averageAccuracy: 0,
      averageTimeToFix: 0,
      successRate: 0,
      recommendations: ['No GPS data available for analysis']
    };
  }

  const accuracies = results.map(r => r.performanceMetrics.finalAccuracy).filter(a => a > 0);
  const times = results.map(r => r.performanceMetrics.timeToFirstFix);
  
  const averageAccuracy = accuracies.length > 0 
    ? accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length 
    : 0;
  
  const averageTimeToFix = times.reduce((sum, time) => sum + time, 0) / times.length;
  const successRate = results.length > 0 ? (accuracies.length / results.length) * 100 : 0;

  const recommendations: string[] = [];

  if (averageAccuracy > 100) {
    recommendations.push('GPS accuracy is poor - consider enabling high accuracy mode');
  } else if (averageAccuracy > 50) {
    recommendations.push('GPS accuracy is fair - try moving to an area with better sky visibility');
  }

  if (averageTimeToFix > 15000) {
    recommendations.push('GPS fix time is slow - consider warming up GPS before use');
  }

  if (successRate < 80) {
    recommendations.push('GPS success rate is low - check device location settings');
  }

  return {
    averageAccuracy: Math.round(averageAccuracy),
    averageTimeToFix: Math.round(averageTimeToFix),
    successRate: Math.round(successRate),
    recommendations
  };
}

/**
 * Smart location selection - choose best location from multiple sources
 */
export function selectBestLocation(locations: LocationResult[]): LocationResult | null {
  if (locations.length === 0) return null;
  if (locations.length === 1) return locations[0];

  // Score each location based on multiple factors
  const scoredLocations = locations.map(location => {
    let score = 0;
    const accuracy = location.coordinates.accuracy || Infinity;
    const age = location.coordinates.timestamp ? Date.now() - location.coordinates.timestamp : Infinity;

    // Accuracy score (higher is better, lower accuracy value is better)
    if (accuracy <= 10) score += 100;
    else if (accuracy <= 50) score += 80;
    else if (accuracy <= 100) score += 60;
    else score += 20;

    // Source score
    if (location.source === 'gps') score += 30;
    else if (location.source === 'network') score += 20;
    else if (location.source === 'cache') score += 10;

    // Recency score
    if (age <= 30000) score += 20; // 30 seconds
    else if (age <= 300000) score += 10; // 5 minutes
    else score += 5;

    return { location, score };
  });

  // Sort by score and return the best
  scoredLocations.sort((a, b) => b.score - a.score);
  return scoredLocations[0].location;
}

/**
 * Preload GPS for faster subsequent requests
 */
export async function preloadGPS(): Promise<void> {
  try {
    await geolocationService.warmUpGPS();
    console.log('GPS preloaded successfully');
  } catch (error) {
    console.warn('GPS preload failed:', error);
  }
}
