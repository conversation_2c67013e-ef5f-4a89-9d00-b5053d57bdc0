/**
 * Advanced Location Cache Service for HK Transit Hub
 * Provides intelligent location caching with accuracy-based selection and nearby location detection
 */

import { LocationCoordinates } from './geolocationService';

export interface CachedLocationEntry {
  coordinates: LocationCoordinates;
  displayName: string;
  timestamp: number;
  accuracy: number;
  source: string;
  usageCount: number;
  lastUsed: number;
}

export interface LocationCacheStats {
  totalEntries: number;
  averageAccuracy: number;
  oldestEntry: number;
  newestEntry: number;
  memoryUsage: string;
  hitRate: number;
}

class LocationCache {
  private cache = new Map<string, CachedLocationEntry>();
  private readonly MAX_ENTRIES = 50;
  private readonly MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours
  private readonly NEARBY_THRESHOLD = 100; // meters
  private hits = 0;
  private misses = 0;

  /**
   * Generate a cache key for coordinates
   */
  private generateKey(coordinates: LocationCoordinates): string {
    // Round to ~11m precision for caching nearby locations together
    const lat = Math.round(coordinates.lat * 10000) / 10000;
    const lng = Math.round(coordinates.lng * 10000) / 10000;
    return `${lat},${lng}`;
  }

  /**
   * Calculate distance between two coordinates in meters
   */
  private calculateDistance(coord1: LocationCoordinates, coord2: LocationCoordinates): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = coord1.lat * Math.PI / 180;
    const φ2 = coord2.lat * Math.PI / 180;
    const Δφ = (coord2.lat - coord1.lat) * Math.PI / 180;
    const Δλ = (coord2.lng - coord1.lng) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * Clean up expired and excess entries
   */
  private cleanup(): void {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());

    // Remove expired entries
    for (const [key, entry] of entries) {
      if (now - entry.timestamp > this.MAX_AGE) {
        this.cache.delete(key);
      }
    }

    // If still over limit, remove least recently used entries
    if (this.cache.size > this.MAX_ENTRIES) {
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].lastUsed - b[1].lastUsed);
      
      const toRemove = this.cache.size - this.MAX_ENTRIES;
      for (let i = 0; i < toRemove; i++) {
        this.cache.delete(sortedEntries[i][0]);
      }
    }
  }

  /**
   * Set a location in the cache
   */
  set(coordinates: LocationCoordinates, displayName: string, source: string): void {
    this.cleanup();

    const key = this.generateKey(coordinates);
    const now = Date.now();
    const accuracy = coordinates.accuracy || 0;

    const existing = this.cache.get(key);
    if (existing) {
      // Update if this location is more accurate or more recent
      if (accuracy > 0 && (existing.accuracy === 0 || accuracy < existing.accuracy)) {
        existing.coordinates = coordinates;
        existing.accuracy = accuracy;
        existing.timestamp = now;
        existing.lastUsed = now;
        existing.usageCount++;
        existing.source = source;
        existing.displayName = displayName;
      } else {
        // Just update usage stats
        existing.lastUsed = now;
        existing.usageCount++;
      }
    } else {
      // Add new entry
      this.cache.set(key, {
        coordinates,
        displayName,
        timestamp: now,
        accuracy,
        source,
        usageCount: 1,
        lastUsed: now
      });
    }
  }

  /**
   * Get the best cached location (most accurate and recent)
   */
  getBest(): CachedLocationEntry | null {
    this.cleanup();

    if (this.cache.size === 0) {
      this.misses++;
      return null;
    }

    const entries = Array.from(this.cache.values());
    const now = Date.now();

    // Score entries based on accuracy, recency, and usage
    const scoredEntries = entries.map(entry => {
      const age = now - entry.timestamp;
      const ageScore = Math.max(0, 1 - (age / (60 * 60 * 1000))); // Decay over 1 hour
      const accuracyScore = entry.accuracy > 0 ? Math.max(0, 1 - (entry.accuracy / 100)) : 0.5;
      const usageScore = Math.min(1, entry.usageCount / 10);
      
      const totalScore = (accuracyScore * 0.5) + (ageScore * 0.3) + (usageScore * 0.2);
      
      return { entry, score: totalScore };
    });

    // Sort by score and return the best
    scoredEntries.sort((a, b) => b.score - a.score);
    const best = scoredEntries[0]?.entry;

    if (best) {
      this.hits++;
      best.lastUsed = now;
      best.usageCount++;
      return best;
    }

    this.misses++;
    return null;
  }

  /**
   * Check if there's a nearby cached location
   */
  hasNearby(coordinates: LocationCoordinates, radiusMeters: number = this.NEARBY_THRESHOLD): CachedLocationEntry | null {
    this.cleanup();

    for (const entry of this.cache.values()) {
      const distance = this.calculateDistance(coordinates, entry.coordinates);
      if (distance <= radiusMeters) {
        entry.lastUsed = Date.now();
        entry.usageCount++;
        this.hits++;
        return entry;
      }
    }

    this.misses++;
    return null;
  }

  /**
   * Get all cached locations within a radius
   */
  getNearby(coordinates: LocationCoordinates, radiusMeters: number = this.NEARBY_THRESHOLD): CachedLocationEntry[] {
    this.cleanup();

    const nearby: CachedLocationEntry[] = [];
    for (const entry of this.cache.values()) {
      const distance = this.calculateDistance(coordinates, entry.coordinates);
      if (distance <= radiusMeters) {
        nearby.push(entry);
      }
    }

    return nearby.sort((a, b) => {
      const distA = this.calculateDistance(coordinates, a.coordinates);
      const distB = this.calculateDistance(coordinates, b.coordinates);
      return distA - distB;
    });
  }

  /**
   * Clear all cached locations
   */
  clear(): void {
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): LocationCacheStats {
    this.cleanup();

    const entries = Array.from(this.cache.values());
    const now = Date.now();

    if (entries.length === 0) {
      return {
        totalEntries: 0,
        averageAccuracy: 0,
        oldestEntry: 0,
        newestEntry: 0,
        memoryUsage: '0 B',
        hitRate: 0
      };
    }

    const accuracies = entries.filter(e => e.accuracy > 0).map(e => e.accuracy);
    const averageAccuracy = accuracies.length > 0 
      ? accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length 
      : 0;

    const timestamps = entries.map(e => e.timestamp);
    const oldestEntry = now - Math.min(...timestamps);
    const newestEntry = now - Math.max(...timestamps);

    const totalRequests = this.hits + this.misses;
    const hitRate = totalRequests > 0 ? this.hits / totalRequests : 0;

    // Estimate memory usage
    const jsonString = JSON.stringify(Array.from(this.cache.entries()));
    const bytes = new Blob([jsonString]).size;
    let memoryUsage: string;
    if (bytes < 1024) {
      memoryUsage = `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      memoryUsage = `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      memoryUsage = `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }

    return {
      totalEntries: entries.length,
      averageAccuracy: Math.round(averageAccuracy),
      oldestEntry,
      newestEntry,
      memoryUsage,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Check if cache is empty
   */
  isEmpty(): boolean {
    return this.cache.size === 0;
  }
}

// Export singleton instance
export const locationCache = new LocationCache();
