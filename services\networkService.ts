/**
 * Network Service for HK Transit Hub
 * Provides network-aware geolocation options and connection monitoring
 */

export interface NetworkInfo {
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

export interface GeolocationNetworkOptions {
  enableHighAccuracy: boolean;
  timeout: number;
  maximumAge: number;
}

class NetworkService {
  private connectionInfo: NetworkInfo | null = null;
  private lastUpdate = 0;
  private readonly UPDATE_INTERVAL = 30000; // 30 seconds

  constructor() {
    this.updateConnectionInfo();
    this.setupConnectionMonitoring();
  }

  /**
   * Update connection information
   */
  private updateConnectionInfo(): void {
    const now = Date.now();
    if (now - this.lastUpdate < this.UPDATE_INTERVAL && this.connectionInfo) {
      return;
    }

    // Use Network Information API if available
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      this.connectionInfo = {
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        saveData: connection.saveData || false
      };
    } else {
      // Fallback: estimate based on user agent and other factors
      this.connectionInfo = this.estimateConnection();
    }

    this.lastUpdate = now;
  }

  /**
   * Estimate connection quality when Network Information API is not available
   */
  private estimateConnection(): NetworkInfo {
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobile = /mobile|android|iphone|ipad/.test(userAgent);
    
    // Conservative estimates for mobile devices
    if (isMobile) {
      return {
        effectiveType: '3g',
        downlink: 1.5,
        rtt: 300,
        saveData: false
      };
    }

    // Assume good connection for desktop
    return {
      effectiveType: '4g',
      downlink: 10,
      rtt: 100,
      saveData: false
    };
  }

  /**
   * Setup connection monitoring
   */
  private setupConnectionMonitoring(): void {
    // Listen for connection changes
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      connection.addEventListener('change', () => {
        this.lastUpdate = 0; // Force update on next call
        this.updateConnectionInfo();
      });
    }

    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.lastUpdate = 0;
      this.updateConnectionInfo();
    });

    window.addEventListener('offline', () => {
      this.connectionInfo = {
        effectiveType: 'offline',
        downlink: 0,
        rtt: 0,
        saveData: true
      };
    });
  }

  /**
   * Check if connection is fast enough for high accuracy GPS
   */
  isFastConnection(): boolean {
    this.updateConnectionInfo();
    
    if (!this.connectionInfo) return true; // Assume good connection if unknown

    const { effectiveType, downlink } = this.connectionInfo;
    
    // Consider 4g, 3g with good downlink as fast
    if (effectiveType === '4g') return true;
    if (effectiveType === '3g' && downlink > 1) return true;
    
    return false;
  }

  /**
   * Check if data saving mode is enabled
   */
  isDataSavingEnabled(): boolean {
    this.updateConnectionInfo();
    return this.connectionInfo?.saveData || false;
  }

  /**
   * Check if device is online
   */
  isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * Get network-optimized geolocation options
   */
  getGeolocationOptions(): GeolocationNetworkOptions {
    this.updateConnectionInfo();

    if (!this.connectionInfo || !this.isOnline()) {
      // Offline or unknown - use conservative settings
      return {
        enableHighAccuracy: false,
        timeout: 30000,
        maximumAge: 600000 // 10 minutes
      };
    }

    const { effectiveType, rtt, saveData } = this.connectionInfo;

    // Data saving mode - use conservative settings
    if (saveData) {
      return {
        enableHighAccuracy: false,
        timeout: 20000,
        maximumAge: 300000 // 5 minutes
      };
    }

    // Optimize based on connection type
    switch (effectiveType) {
      case '4g':
        return {
          enableHighAccuracy: true,
          timeout: 8000,
          maximumAge: 60000 // 1 minute
        };

      case '3g':
        return {
          enableHighAccuracy: rtt < 400, // Enable if RTT is reasonable
          timeout: 12000,
          maximumAge: 120000 // 2 minutes
        };

      case '2g':
      case 'slow-2g':
        return {
          enableHighAccuracy: false,
          timeout: 20000,
          maximumAge: 300000 // 5 minutes
        };

      default:
        // Unknown connection type - use moderate settings
        return {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 120000 // 2 minutes
        };
    }
  }

  /**
   * Get current connection information
   */
  getConnectionInfo(): NetworkInfo | null {
    this.updateConnectionInfo();
    return this.connectionInfo;
  }

  /**
   * Get connection quality score (0-1, higher is better)
   */
  getConnectionQuality(): number {
    this.updateConnectionInfo();

    if (!this.connectionInfo || !this.isOnline()) {
      return 0;
    }

    const { effectiveType, downlink, rtt } = this.connectionInfo;

    let score = 0;

    // Base score from effective type
    switch (effectiveType) {
      case '4g':
        score = 0.9;
        break;
      case '3g':
        score = 0.6;
        break;
      case '2g':
        score = 0.3;
        break;
      case 'slow-2g':
        score = 0.1;
        break;
      default:
        score = 0.5;
    }

    // Adjust based on downlink speed
    if (downlink > 0) {
      if (downlink >= 10) score = Math.min(1, score + 0.1);
      else if (downlink >= 5) score = Math.min(1, score + 0.05);
      else if (downlink < 1) score = Math.max(0, score - 0.2);
    }

    // Adjust based on RTT
    if (rtt > 0) {
      if (rtt <= 100) score = Math.min(1, score + 0.05);
      else if (rtt >= 500) score = Math.max(0, score - 0.1);
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Check if location services should use high accuracy based on network
   */
  shouldUseHighAccuracy(): boolean {
    return this.isFastConnection() && 
           !this.isDataSavingEnabled() && 
           this.getConnectionQuality() > 0.5;
  }

  /**
   * Get recommended timeout based on network conditions
   */
  getRecommendedTimeout(): number {
    const quality = this.getConnectionQuality();
    
    if (quality > 0.8) return 8000;   // Fast connection
    if (quality > 0.5) return 12000;  // Medium connection
    if (quality > 0.2) return 20000;  // Slow connection
    return 30000;                     // Very slow or unknown
  }

  /**
   * Get recommended maximum age based on network conditions
   */
  getRecommendedMaxAge(): number {
    const quality = this.getConnectionQuality();
    
    if (quality > 0.8) return 60000;   // 1 minute for fast connections
    if (quality > 0.5) return 120000;  // 2 minutes for medium
    if (quality > 0.2) return 300000;  // 5 minutes for slow
    return 600000;                     // 10 minutes for very slow
  }
}

// Export singleton instance
export const networkService = new NetworkService();
