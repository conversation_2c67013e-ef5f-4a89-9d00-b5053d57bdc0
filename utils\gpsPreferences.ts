/**
 * GPS Preferences Utilities
 * Centralized GPS preferences management for the HK Transit Hub app
 */

export interface GPSPreferences {
  enableHighAccuracy: boolean;
  useMultipleReadings: boolean;
  enableCaching: boolean;
  enableReverseGeocoding: boolean;
  accuracyThreshold: number;
  timeout: number;
}

export const DEFAULT_GPS_PREFERENCES: GPSPreferences = {
  enableHighAccuracy: true,
  useMultipleReadings: true,
  enableCaching: true,
  enableReverseGeocoding: true,
  accuracyThreshold: 50,
  timeout: 10000
};

const GPS_PREFERENCES_KEY = 'hk-transit-hub-gps-preferences';

/**
 * Load GPS preferences from localStorage
 */
export function loadGPSPreferences(): GPSPreferences {
  try {
    const saved = localStorage.getItem(GPS_PREFERENCES_KEY);
    if (saved) {
      const parsed = JSON.parse(saved);
      return { ...DEFAULT_GPS_PREFERENCES, ...parsed };
    }
  } catch (error) {
    console.warn('Failed to load GPS preferences from localStorage:', error);
  }
  return DEFAULT_GPS_PREFERENCES;
}

/**
 * Save GPS preferences to localStorage
 */
export function saveGPSPreferences(preferences: GPSPreferences): boolean {
  try {
    localStorage.setItem(GPS_PREFERENCES_KEY, JSON.stringify(preferences));
    console.log('GPS preferences saved to localStorage:', preferences);
    return true;
  } catch (error) {
    console.error('Failed to save GPS preferences to localStorage:', error);
    return false;
  }
}

/**
 * Reset GPS preferences to defaults
 */
export function resetGPSPreferences(): GPSPreferences {
  const defaults = { ...DEFAULT_GPS_PREFERENCES };
  saveGPSPreferences(defaults);
  return defaults;
}

/**
 * Get GPS preferences with fallback to defaults
 */
export function getGPSPreferences(): GPSPreferences {
  return loadGPSPreferences();
}

/**
 * Update specific GPS preference
 */
export function updateGPSPreference<K extends keyof GPSPreferences>(
  key: K,
  value: GPSPreferences[K]
): GPSPreferences {
  const current = loadGPSPreferences();
  const updated = { ...current, [key]: value };
  saveGPSPreferences(updated);
  return updated;
}

/**
 * Check if GPS preferences are set to high accuracy mode
 */
export function isHighAccuracyMode(): boolean {
  const preferences = loadGPSPreferences();
  return preferences.enableHighAccuracy;
}

/**
 * Check if multiple readings are enabled
 */
export function isMultipleReadingsEnabled(): boolean {
  const preferences = loadGPSPreferences();
  return preferences.useMultipleReadings;
}

/**
 * Get accuracy threshold from preferences
 */
export function getAccuracyThreshold(): number {
  const preferences = loadGPSPreferences();
  return preferences.accuracyThreshold;
}

/**
 * Get timeout from preferences
 */
export function getTimeout(): number {
  const preferences = loadGPSPreferences();
  return preferences.timeout;
}

/**
 * Export GPS preferences as JSON string
 */
export function exportGPSPreferences(): string {
  const preferences = loadGPSPreferences();
  return JSON.stringify(preferences, null, 2);
}

/**
 * Import GPS preferences from JSON string
 */
export function importGPSPreferences(jsonString: string): GPSPreferences {
  try {
    const imported = JSON.parse(jsonString);
    const validated = { ...DEFAULT_GPS_PREFERENCES, ...imported };
    saveGPSPreferences(validated);
    return validated;
  } catch (error) {
    console.error('Failed to import GPS preferences:', error);
    throw new Error('Invalid GPS preferences format');
  }
}
